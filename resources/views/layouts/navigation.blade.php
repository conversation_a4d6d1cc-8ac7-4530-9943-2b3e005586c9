<nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
    <div class="container">
        <!-- Logo -->
        <a class="navbar-brand" href="{{ route('home') }}">
            <span class="bg-edunetra-yellow text-dark px-3 py-2 rounded fw-bold">
                edunetra.id
            </span>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link fw-semibold {{ request()->routeIs('home') ? 'active' : '' }}"
                       href="{{ route('home') }}">HOME</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold {{ request()->routeIs('courses.*') ? 'active' : '' }}"
                       href="{{ route('courses.public') }}">COURSES</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold {{ request()->routeIs('about') ? 'active' : '' }}"
                       href="{{ route('about') }}">ABOUT US</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold {{ request()->routeIs('contact') ? 'active' : '' }}"
                       href="{{ route('contact') }}">CONTACT US</a>
                </li>
            </ul>

            <!-- Auth Links -->
            <ul class="navbar-nav">
                @auth
                    <!-- User Dashboard Link based on role -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ Auth::user()->name }}
                        </a>
                        <ul class="dropdown-menu">
                            @if(auth()->user()->isAdmin())
                                <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> Admin Panel</a></li>
                            @elseif(auth()->user()->isTeacher())
                                <li><a class="dropdown-item" href="#"><i class="bi bi-book"></i> Teacher Dashboard</a></li>
                            @else 
                            <li><a class="dropdown-item" href="{{ route('my-courses') }}"><i class="bi bi-journal-bookmark"></i> My Courses</a></li>
                            @endif
                            <li><a class="dropdown-item" href="{{ route('profile.edit') }}"><i class="bi bi-person"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Log Out
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                @else
                    <li class="nav-item">
                        <a href="{{ route('login') }}" class="btn btn-edunetra ms-2">
                            <i class="bi bi-box-arrow-in-right"></i> LOGIN
                        </a>
                    </li>
                @endauth
            </ul>
        </div>
    </div>
</nav>


