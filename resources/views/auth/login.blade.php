<x-guest-layout>
    <div class="login-card p-5 animate-fade-in">
        <!-- Header -->
        <div class="text-center mb-5">
            <a href="{{ route('home') }}" class="text-decoration-none">
                <span class="bg-edunetra-yellow text-dark px-4 py-2 rounded fw-bold fs-3">
                    edunetra.id
                </span>
            </a>
            <h2 class="mt-4 mb-2 fw-bold text-edunetra-dark">Selamat Datang Kembali</h2>
            <p class="text-muted">Pilih cara masuk yang sesuai dengan kebutuhan Anda</p>
        </div>

        <!-- Login Options -->
        <div class="row g-4 mb-5">
            <!-- Member Login Option -->
            <div class="col-md-6">
                <div class="login-option-card card border-0 shadow-sm h-100 p-4" data-login-type="member">
                    <div class="card-body text-center p-0">
                        <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="bi bi-person-fill text-primary fs-2"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Login sebagai Member</h5>
                        <p class="text-muted mb-3">
                            Untuk peserta didik dan pengajar yang sudah terdaftar
                        </p>
                        <div class="d-flex justify-content-center gap-2">
                            <span class="badge bg-primary bg-opacity-10 text-primary">Student</span>
                            <span class="badge bg-success bg-opacity-10 text-success">Teacher</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Guest Access Option -->
            <div class="col-md-6">
                <div class="login-option-card card border-0 shadow-sm h-100 p-4" data-login-type="guest">
                    <div class="card-body text-center p-0">
                        <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="bi bi-eye-fill text-warning fs-2"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Akses sebagai Guest</h5>
                        <p class="text-muted mb-3">
                            Jelajahi konten publik tanpa perlu mendaftar
                        </p>
                        <div class="d-flex justify-content-center">
                            <span class="badge bg-warning bg-opacity-10 text-warning">Public Access</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Member Login Form -->
        <div id="member-login-form" style="display: none;">
            <!-- Session Status -->
            @if (session('status'))
                <div class="alert alert-success mb-4" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <form method="POST" action="{{ route('login') }}">
                @csrf

                <!-- Email Address -->
                <div class="mb-3">
                    <label for="email" class="form-label fw-semibold">
                        <i class="bi bi-envelope me-2"></i>Email Address
                    </label>
                    <input type="email"
                           class="form-control form-control-lg @error('email') is-invalid @enderror"
                           id="email"
                           name="email"
                           value="{{ old('email') }}"
                           required
                           autofocus
                           autocomplete="username"
                           placeholder="Masukkan email Anda">
                    @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Password -->
                <div class="mb-3">
                    <label for="password" class="form-label fw-semibold">
                        <i class="bi bi-lock me-2"></i>Password
                    </label>
                    <div class="input-group">
                        <input type="password"
                               class="form-control form-control-lg @error('password') is-invalid @enderror"
                               id="password"
                               name="password"
                               required
                               autocomplete="current-password"
                               placeholder="Masukkan password Anda">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="bi bi-eye"></i>
                        </button>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Remember Me -->
                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember_me" name="remember">
                        <label class="form-check-label" for="remember_me">
                            Ingat saya
                        </label>
                    </div>
                </div>

                <!-- Login Button -->
                <div class="d-grid mb-4">
                    <button type="submit" class="btn btn-edunetra btn-lg">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        Masuk sebagai Member
                    </button>
                </div>

                <!-- Additional Links -->
                <div class="text-center">
                    @if (Route::has('password.request'))
                        <a href="{{ route('password.request') }}" class="text-decoration-none text-muted me-3">
                            <i class="bi bi-question-circle me-1"></i>Lupa Password?
                        </a>
                    @endif
                    @if (Route::has('register'))
                        <a href="{{ route('register') }}" class="text-decoration-none text-primary">
                            <i class="bi bi-person-plus me-1"></i>Daftar Akun Baru
                        </a>
                    @endif
                </div>
            </form>
        </div>

        <!-- Guest Access Info -->
        <div id="guest-access-info" style="display: none;">
            <div class="text-center">
                <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 100px; height: 100px;">
                    <i class="bi bi-globe text-warning display-4"></i>
                </div>
                <h4 class="fw-bold mb-3">Akses Guest</h4>
                <p class="text-muted mb-4">
                    Sebagai guest, Anda dapat menjelajahi konten publik kami termasuk informasi kursus,
                    artikel, dan resources yang tersedia untuk umum.
                </p>

                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <span>Lihat Katalog Kursus</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <span>Baca Artikel & Tips</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <span>Informasi Program</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <span>Kontak & Support</span>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <a href="{{ route('home') }}" class="btn btn-warning btn-lg">
                        <i class="bi bi-house me-2"></i>
                        Lanjutkan sebagai Guest
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="showMemberLogin()">
                        <i class="bi bi-person me-2"></i>
                        Atau Login sebagai Member
                    </button>
                </div>
            </div>
        </div>

        <!-- Back to Options -->
        <div id="back-to-options" class="text-center mt-4" style="display: none;">
            <button type="button" class="btn btn-outline-secondary" onclick="showLoginOptions()">
                <i class="bi bi-arrow-left me-2"></i>
                Kembali ke Pilihan Login
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginOptions = document.querySelectorAll('.login-option-card');
            const memberForm = document.getElementById('member-login-form');
            const guestInfo = document.getElementById('guest-access-info');
            const backButton = document.getElementById('back-to-options');
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');

            // Handle login option selection
            loginOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const loginType = this.dataset.loginType;

                    // Hide options
                    loginOptions.forEach(opt => opt.parentElement.style.display = 'none');

                    if (loginType === 'member') {
                        memberForm.style.display = 'block';
                    } else if (loginType === 'guest') {
                        guestInfo.style.display = 'block';
                    }

                    backButton.style.display = 'block';
                });
            });

            // Password toggle
            if (togglePassword && passwordInput) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);

                    const icon = this.querySelector('i');
                    icon.classList.toggle('bi-eye');
                    icon.classList.toggle('bi-eye-slash');
                });
            }
        });

        function showLoginOptions() {
            document.querySelectorAll('.login-option-card').forEach(opt => {
                opt.parentElement.style.display = 'block';
            });
            document.getElementById('member-login-form').style.display = 'none';
            document.getElementById('guest-access-info').style.display = 'none';
            document.getElementById('back-to-options').style.display = 'none';
        }

        function showMemberLogin() {
            document.getElementById('guest-access-info').style.display = 'none';
            document.getElementById('member-login-form').style.display = 'block';
        }
    </script>
</x-guest-layout>
