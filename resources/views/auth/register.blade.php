<x-guest-layout>
    <div class="login-card p-5 animate-fade-in">
        <!-- Header -->
        <div class="text-center mb-5">
            <a href="{{ route('home') }}" class="text-decoration-none">
                <span class="bg-edunetra-yellow text-dark px-4 py-2 rounded fw-bold fs-3">
                    edunetra.id
                </span>
            </a>
            <h2 class="mt-4 mb-2 fw-bold text-edunetra-dark">Daftar Akun Baru</h2>
            <p class="text-muted">Bergabunglah dengan komunitas pembelajaran inklusif kami</p>
        </div>

        <!-- Role Selection -->
        <div class="row g-3 mb-4">
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="role" id="student" value="student" checked>
                    <label class="form-check-label w-100" for="student">
                        <div class="card border-2 h-100 p-3">
                            <div class="text-center">
                                <i class="bi bi-mortarboard text-primary fs-3 mb-2"></i>
                                <h6 class="fw-bold mb-1">Peserta Didik</h6>
                                <small class="text-muted">Saya ingin belajar</small>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="role" id="teacher" value="teacher">
                    <label class="form-check-label w-100" for="teacher">
                        <div class="card border-2 h-100 p-3">
                            <div class="text-center">
                                <i class="bi bi-person-workspace text-success fs-3 mb-2"></i>
                                <h6 class="fw-bold mb-1">Pengajar</h6>
                                <small class="text-muted">Saya ingin mengajar</small>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('register') }}">
            @csrf

            <!-- Hidden Role Field -->
            <input type="hidden" name="role" id="selected_role" value="student">

            <!-- Name -->
            <div class="mb-3">
                <label for="name" class="form-label fw-semibold">
                    <i class="bi bi-person me-2"></i>Nama Lengkap
                </label>
                <input type="text"
                       class="form-control form-control-lg @error('name') is-invalid @enderror"
                       id="name"
                       name="name"
                       value="{{ old('name') }}"
                       required
                       autofocus
                       autocomplete="name"
                       placeholder="Masukkan nama lengkap Anda">
                @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Email Address -->
            <div class="mb-3">
                <label for="email" class="form-label fw-semibold">
                    <i class="bi bi-envelope me-2"></i>Email Address
                </label>
                <input type="email"
                       class="form-control form-control-lg @error('email') is-invalid @enderror"
                       id="email"
                       name="email"
                       value="{{ old('email') }}"
                       required
                       autocomplete="username"
                       placeholder="Masukkan email Anda">
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Password -->
            <div class="mb-3">
                <label for="password" class="form-label fw-semibold">
                    <i class="bi bi-lock me-2"></i>Password
                </label>
                <div class="input-group">
                    <input type="password"
                           class="form-control form-control-lg @error('password') is-invalid @enderror"
                           id="password"
                           name="password"
                           required
                           autocomplete="new-password"
                           placeholder="Minimal 8 karakter">
                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                        <i class="bi bi-eye"></i>
                    </button>
                    @error('password')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Confirm Password -->
            <div class="mb-4">
                <label for="password_confirmation" class="form-label fw-semibold">
                    <i class="bi bi-shield-check me-2"></i>Konfirmasi Password
                </label>
                <div class="input-group">
                    <input type="password"
                           class="form-control form-control-lg @error('password_confirmation') is-invalid @enderror"
                           id="password_confirmation"
                           name="password_confirmation"
                           required
                           autocomplete="new-password"
                           placeholder="Ulangi password Anda">
                    <button class="btn btn-outline-secondary" type="button" id="togglePasswordConfirm">
                        <i class="bi bi-eye"></i>
                    </button>
                    @error('password_confirmation')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Terms Agreement -->
            <div class="mb-4">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="terms" required>
                    <label class="form-check-label" for="terms">
                        Saya setuju dengan <a href="#" class="text-decoration-none">Syarat & Ketentuan</a>
                        dan <a href="#" class="text-decoration-none">Kebijakan Privasi</a>
                    </label>
                </div>
            </div>

            <!-- Register Button -->
            <div class="d-grid mb-4">
                <button type="submit" class="btn btn-edunetra btn-lg">
                    <i class="bi bi-person-plus me-2"></i>
                    Daftar Sekarang
                </button>
            </div>

            <!-- Login Link -->
            <div class="text-center">
                <span class="text-muted">Sudah punya akun? </span>
                <a href="{{ route('login') }}" class="text-decoration-none text-primary fw-semibold">
                    <i class="bi bi-box-arrow-in-right me-1"></i>Login di sini
                </a>
            </div>
        </form>
    </div>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const roleRadios = document.querySelectorAll('input[name="role"]');
            const selectedRoleInput = document.getElementById('selected_role');
            const togglePassword = document.getElementById('togglePassword');
            const togglePasswordConfirm = document.getElementById('togglePasswordConfirm');
            const passwordInput = document.getElementById('password');
            const passwordConfirmInput = document.getElementById('password_confirmation');

            // Handle role selection
            roleRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    selectedRoleInput.value = this.value;

                    // Update card styles
                    roleRadios.forEach(r => {
                        const card = r.nextElementSibling.querySelector('.card');
                        if (r.checked) {
                            card.classList.add('border-primary');
                            card.classList.remove('border-2');
                            card.classList.add('border-3');
                        } else {
                            card.classList.remove('border-primary', 'border-3');
                            card.classList.add('border-2');
                        }
                    });
                });
            });

            // Password toggle functionality
            function setupPasswordToggle(toggleBtn, passwordField) {
                if (toggleBtn && passwordField) {
                    toggleBtn.addEventListener('click', function() {
                        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                        passwordField.setAttribute('type', type);

                        const icon = this.querySelector('i');
                        icon.classList.toggle('bi-eye');
                        icon.classList.toggle('bi-eye-slash');
                    });
                }
            }

            setupPasswordToggle(togglePassword, passwordInput);
            setupPasswordToggle(togglePasswordConfirm, passwordConfirmInput);

            // Initialize first role selection
            document.getElementById('student').dispatchEvent(new Event('change'));
        });
    </script>
</x-guest-layout>
