@extends('layouts.app')

@section('title', 'All Courses - Edunetra')

@section('content')
<!-- <PERSON> Header -->
<section class="bg-edunetra-dark text-white py-5">
    <div class="container text-center">
        <h1 class="display-4 fw-bold mb-3">All Courses</h1>
        <p class="fs-5 opacity-75">Explore our comprehensive learning programs</p>
    </div>
</section>

<!-- Filters and Search -->
<section class="py-4 bg-light">
    <div class="container">
        <form action="{{ route('courses.public') }}" method="GET">
            <div class="row g-3 align-items-center">
                <!-- Search -->
                <div class="col-lg-6">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="Search courses..."
                               value="{{ request('search') }}">
                        <button class="btn btn-edunetra" type="submit">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="col-lg-6">
                    <div class="row g-2">
                        <div class="col-md-4">
                            <select name="level" class="form-select">
                                <option value="">All Levels</option>
                                <option value="beginner" {{ request('level') == 'beginner' ? 'selected' : '' }}>Beginner</option>
                                <option value="intermediate" {{ request('level') == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                <option value="advanced" {{ request('level') == 'advanced' ? 'selected' : '' }}>Advanced</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select name="sort" class="form-select">
                                <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest</option>
                                <option value="title" {{ request('sort') == 'title' ? 'selected' : '' }}>Title A-Z</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-outline-edunetra w-100">
                                <i class="bi bi-funnel me-1"></i>Filter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>

<!-- Courses Grid -->
<section class="py-5">
    <div class="container">
        @if($courses->count() > 0)
            <!-- Results Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <p class="text-muted mb-0">
                            Showing {{ $courses->firstItem() }}-{{ $courses->lastItem() }} of {{ $courses->total() }} courses
                        </p>
                        @if(request('search') || request('level'))
                            <a href="{{ route('courses.public') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-x-circle me-1"></i>Clear Filters
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <div class="row g-4">
                @foreach($courses as $course)
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <div class="card h-100 border-0 shadow-sm course-card">
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                @if($course->image)
                                    <img src="{{ $course->image }}" alt="{{ $course->title }}" class="img-fluid rounded-top" style="object-fit: cover; height: 100%; width: 100%;">
                                @else
                                    <div class="text-center text-muted">
                                        <i class="bi bi-book fs-1 mb-2"></i>
                                        <p class="mb-0 small">{{ Str::limit($course->title, 20) }}</p>
                                    </div>
                                @endif
                            </div>

                            <div class="card-body d-flex flex-column">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-edunetra-yellow text-dark">
                                        {{ ucfirst($course->level) }}
                                    </span>
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> {{ $course->duration_weeks }} weeks
                                    </small>
                                </div>

                                <h6 class="card-title fw-bold mb-2">{{ $course->title }}</h6>
                                <p class="card-text text-muted small flex-grow-1">{{ Str::limit($course->description, 80) }}</p>

                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-muted">
                                            <i class="bi bi-person"></i> {{ $course->teacher->name }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-people"></i> {{ $course->getEnrolledStudentsCount() }} students
                                        </small>
                                    </div>
                                    @if($course->start_date)
                                        <small class="text-muted d-block">
                                            <i class="bi bi-calendar"></i> Start: {{ $course->start_date->format('M d, Y') }}
                                        </small>
                                    @endif
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-bold">
                                        @if($course->price > 0)
                                            Rp {{ number_format($course->price, 0, ',', '.') }}
                                        @else
                                            <span class="text-success">Free</span>
                                        @endif
                                    </span>
                                    <a href="{{ route('courses.show', $course->slug) }}"
                                       class="btn btn-edunetra btn-sm">
                                        <i class="bi bi-eye me-1"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="row mt-5">
                <div class="col-12 d-flex justify-content-center">
                    {{ $courses->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="bi bi-search display-1 text-muted mb-3"></i>
                <h3 class="fw-bold mb-2">No courses found</h3>
                <p class="text-muted mb-3">
                    @if(request('search'))
                        No courses match your search for "{{ request('search') }}".
                    @else
                        Try adjusting your search or filter criteria.
                    @endif
                </p>
                <a href="{{ route('courses.public') }}" class="btn btn-edunetra">
                    <i class="bi bi-arrow-left me-1"></i>View All Courses
                </a>
            </div>
        @endif
    </div>
</section>
@endsection
