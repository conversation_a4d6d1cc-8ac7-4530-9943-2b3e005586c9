@extends('layouts.app')

@section('title', 'My Courses - Edunetra')

@section('content')
<!-- Page Header -->
<section class="bg-edunetra-dark text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">My Courses</h1>
                <p class="fs-5 text-light opacity-90">Track your learning progress and continue your education journey</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{{ route('courses.public') }}" class="btn btn-edunetra btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>Browse More Courses
                </a>
            </div>
        </div>
    </div>
</section>

<!-- My Courses Content -->
<section class="py-5 bg-light">
    <div class="container">
        @if($enrollments->count() > 0)
            <div class="row g-4">
                @foreach($enrollments as $enrollment)
                    @php
                        $course = $enrollment->course;
                        $progressPercentage = $enrollment->progress;
                    @endphp
                    <div class="col-lg-6 col-xl-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 180px;">
                                @if($course->image)
                                    <img src="{{ $course->image }}" alt="{{ $course->title }}" class="img-fluid rounded-top" style="object-fit: cover; height: 100%; width: 100%;">
                                @else
                                    <div class="text-center text-muted">
                                        <i class="bi bi-book fs-1 mb-2"></i>
                                        <p class="mb-0 small">{{ Str::limit($course->title, 20) }}</p>
                                    </div>
                                @endif
                            </div>

                            <div class="card-body d-flex flex-column">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-edunetra-yellow text-dark">
                                        {{ ucfirst($course->level) }}
                                    </span>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar"></i> Enrolled {{ $enrollment->enrolled_at->format('M d, Y') }}
                                    </small>
                                </div>

                                <h5 class="card-title fw-bold mb-2">{{ $course->title }}</h5>
                                <p class="card-text text-muted small flex-grow-1">{{ Str::limit($course->description, 80) }}</p>

                                <!-- Progress Bar -->
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-muted">Progress</small>
                                        <small class="text-muted">{{ $progressPercentage }}%</small>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-edunetra-yellow" role="progressbar" 
                                             style="width: {{ $progressPercentage }}%" 
                                             aria-valuenow="{{ $progressPercentage }}" 
                                             aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>

                                <!-- Course Info -->
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small class="text-muted">
                                            <i class="bi bi-person"></i> {{ $course->teacher->name }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-list-ol"></i> {{ $course->sessions->count() }} sessions
                                        </small>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-grid gap-2">
                                    <a href="{{ route('courses.show', $course->slug) }}" class="btn btn-edunetra">
                                        @if($progressPercentage > 0)
                                            <i class="bi bi-play-circle me-1"></i>Continue Learning
                                        @else
                                            <i class="bi bi-play-circle me-1"></i>Start Course
                                        @endif
                                    </a>
                                    <a href="{{ route('courses.progress', $course) }}" class="btn btn-outline-edunetra btn-sm">
                                        <i class="bi bi-graph-up me-1"></i>View Progress
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-5">
                <i class="bi bi-collection display-1 text-muted mb-3"></i>
                <h3 class="fw-bold mb-2">No Enrolled Courses</h3>
                <p class="text-muted mb-4">You haven't enrolled in any courses yet. Start your learning journey today!</p>
                <a href="{{ route('courses.public') }}" class="btn btn-edunetra btn-lg">
                    <i class="bi bi-search me-2"></i>Browse Available Courses
                </a>
            </div>
        @endif
    </div>
</section>
@endsection
