<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'slug' => $this->faker->slug(),
            'level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced']),
            'status' => 'published',
            'duration_weeks' => $this->faker->numberBetween(4, 12),
            'price' => $this->faker->randomElement([0, 100000, 250000, 500000]),
            'teacher_id' => User::factory()->create(['role' => 'teacher'])->id,
            'max_students' => $this->faker->numberBetween(10, 50),
            'start_date' => $this->faker->dateTimeBetween('now', '+1 month'),
            'end_date' => $this->faker->dateTimeBetween('+1 month', '+6 months'),
            'tags' => ['english', 'learning', 'accessibility'],
        ];
    }
}
