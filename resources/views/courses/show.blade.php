@extends('layouts.app')

@section('title', $course->title . ' - Edunetra')

@section('content')
<!-- Course Header -->
<section class="bg-edunetra-dark text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="d-flex align-items-center mb-3">
                    <span class="badge bg-edunetra-yellow text-dark me-3 px-3 py-2">
                        {{ ucfirst($course->level) }}
                    </span>
                    <span class="text-light opacity-75">
                        <i class="bi bi-clock me-1"></i> {{ $course->duration_weeks }} weeks
                    </span>
                    <span class="text-light opacity-75 ms-3">
                        <i class="bi bi-list-ol me-1"></i> {{ $course->sessions->count() }} sessions
                    </span>
                </div>
                <h1 class="display-4 fw-bold mb-3">{{ $course->title }}</h1>
                <p class="fs-5 text-light opacity-90 mb-4">{{ $course->description }}</p>
                <div class="d-flex flex-wrap align-items-center text-light opacity-75 mb-4">
                    <span class="me-4 mb-2">
                        <i class="bi bi-person-circle me-2"></i>{{ $course->teacher->name }}
                    </span>
                    <span class="me-4 mb-2">
                        <i class="bi bi-people me-2"></i>{{ $course->getEnrolledStudentsCount() }} students enrolled
                    </span>
                    <span class="mb-2">
                        <i class="bi bi-calendar me-2"></i>{{ $course->start_date ? $course->start_date->format('M d, Y') : 'Flexible start' }}
                    </span>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <div class="display-6 fw-bold text-edunetra-dark mb-2">
                                @if($course->price > 0)
                                    Rp {{ number_format($course->price, 0, ',', '.') }}
                                @else
                                    <span class="text-success">Free</span>
                                @endif
                            </div>
                            <p class="text-muted mb-0">Full course access</p>
                        </div>

                        @auth
                            @if($isEnrolled)
                                <div class="text-center">
                                    <div class="alert alert-success d-flex align-items-center mb-3">
                                        <i class="bi bi-check-circle-fill me-2"></i>
                                        <span>You are enrolled in this course</span>
                                    </div>
                                    <div class="mb-4">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">Progress</small>
                                            <small class="text-muted">{{ $enrollment->progress }}%</small>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-edunetra-yellow" role="progressbar"
                                                 style="width: {{ $enrollment->progress }}%"
                                                 aria-valuenow="{{ $enrollment->progress }}"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                    <a href="#sessions" class="btn btn-edunetra w-100 btn-lg">
                                        <i class="bi bi-play-circle me-2"></i>Continue Learning
                                    </a>
                                    <form action="{{ route('courses.unenroll', $course) }}" method="POST" class="mt-2">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger btn-sm w-100"
                                                onclick="return confirm('Are you sure you want to unenroll from this course?')">
                                            <i class="bi bi-x-circle me-1"></i>Unenroll
                                        </button>
                                    </form>
                                </div>
                            @else
                                <form action="{{ route('courses.enroll', $course) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-edunetra w-100 btn-lg">
                                        <i class="bi bi-plus-circle me-2"></i>Enroll Now
                                    </button>
                                </form>
                            @endif
                        @else
                            <div class="text-center">
                                <a href="{{ route('login') }}" class="btn btn-edunetra w-100 btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Login to Enroll
                                </a>
                                <p class="text-muted mt-3 mb-0">
                                    Don't have an account? <a href="{{ route('register') }}" class="text-edunetra-dark text-decoration-none fw-semibold">Sign up</a>
                                </p>
                            </div>
                        @endauth
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Course Content -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Course Description -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h2 class="h3 fw-bold text-edunetra-dark mb-3">
                            <i class="bi bi-info-circle me-2"></i>About This Course
                        </h2>
                        <div class="text-muted">
                            <p class="mb-3">{{ $course->description }}</p>
                            <p class="mb-0">
                                This comprehensive {{ $course->duration_weeks }}-week program is designed to provide students with
                                a solid foundation in English learning, specifically tailored for young learners with visual impairments.
                                Our innovative teaching methods ensure accessibility while maintaining high educational standards.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Course Sessions -->
                <div id="sessions" class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h2 class="h3 fw-bold text-edunetra-dark mb-0">
                            <i class="bi bi-list-ol me-2"></i>Course Sessions
                        </h2>
                    </div>
                    <div class="card-body p-0">
                        @if($course->sessions->count() > 0)
                            @foreach($course->sessions as $index => $session)
                                <div class="border-bottom p-4 {{ $loop->last ? 'border-0' : '' }}">
                                    <div class="row align-items-center">
                                        <div class="col-lg-8">
                                            <div class="d-flex align-items-start">
                                                <div class="me-3">
                                                    <span class="badge bg-edunetra-yellow text-dark rounded-circle" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                        {{ $session->session_number }}
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h5 class="fw-bold mb-2">{{ $session->title }}</h5>
                                                    <p class="text-muted mb-2">{{ $session->description }}</p>
                                                    <div class="d-flex align-items-center text-sm">
                                                        <span class="text-muted me-3">
                                                            <i class="bi bi-clock me-1"></i>{{ $session->duration_minutes }} minutes
                                                        </span>
                                                        @if($isEnrolled && $session->isCompleted(auth()->id()))
                                                            <span class="badge bg-success">
                                                                <i class="bi bi-check-circle me-1"></i>Completed
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                                            @if($isEnrolled)
                                                <a href="#" class="btn btn-edunetra btn-sm">
                                                    @if($session->isCompleted(auth()->id()))
                                                        <i class="bi bi-arrow-clockwise me-1"></i>Review
                                                    @else
                                                        <i class="bi bi-play-circle me-1"></i>Start
                                                    @endif
                                                </a>
                                            @else
                                                <span class="text-muted">
                                                    <i class="bi bi-lock me-1"></i>Locked
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="p-4 text-center text-muted">
                                <i class="bi bi-list-ol fs-1 mb-3"></i>
                                <p class="mb-0">No sessions available yet.</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Instructor -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h2 class="h3 fw-bold text-edunetra-dark mb-3">
                            <i class="bi bi-person-circle me-2"></i>Your Instructor
                        </h2>
                        <div class="d-flex align-items-start">
                            <div class="me-3">
                                <div class="bg-edunetra-yellow rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 60px; height: 60px;">
                                    <span class="text-dark fw-bold fs-4">{{ substr($course->teacher->name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div>
                                <h4 class="fw-bold mb-2">{{ $course->teacher->name }}</h4>
                                <p class="text-muted mb-0">{{ $course->teacher->bio ?? 'Experienced educator specializing in inclusive learning and accessible education for students with visual impairments.' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Course Info -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h3 class="h5 fw-bold text-edunetra-dark mb-3">
                            <i class="bi bi-info-square me-2"></i>Course Information
                        </h3>
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="bi bi-clock text-edunetra-dark fs-4 mb-2"></i>
                                    <div class="fw-bold">{{ $course->duration_weeks }}</div>
                                    <small class="text-muted">Weeks</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="bi bi-list-ol text-edunetra-dark fs-4 mb-2"></i>
                                    <div class="fw-bold">{{ $course->sessions->count() }}</div>
                                    <small class="text-muted">Sessions</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="bi bi-bar-chart text-edunetra-dark fs-4 mb-2"></i>
                                    <div class="fw-bold">{{ ucfirst($course->level) }}</div>
                                    <small class="text-muted">Level</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="bi bi-people text-edunetra-dark fs-4 mb-2"></i>
                                    <div class="fw-bold">{{ $course->getEnrolledStudentsCount() }}</div>
                                    <small class="text-muted">Students</small>
                                </div>
                            </div>
                        </div>
                        @if($course->start_date)
                            <div class="mt-3 p-3 bg-light rounded text-center">
                                <i class="bi bi-calendar text-edunetra-dark fs-5 mb-2"></i>
                                <div class="fw-bold">{{ $course->start_date->format('M d, Y') }}</div>
                                <small class="text-muted">Start Date</small>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Tags -->
                @if($course->tags)
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-body p-4">
                            <h3 class="h5 fw-bold text-edunetra-dark mb-3">
                                <i class="bi bi-tags me-2"></i>Tags
                            </h3>
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($course->tags as $tag)
                                    <span class="badge bg-edunetra-yellow text-dark px-3 py-2">{{ $tag }}</span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="h5 fw-bold text-edunetra-dark mb-3">
                            <i class="bi bi-lightning me-2"></i>Quick Actions
                        </h3>
                        <div class="d-grid gap-2">
                            @auth
                                @if($isEnrolled)
                                    <a href="{{ route('my-courses') }}" class="btn btn-outline-edunetra btn-sm">
                                        <i class="bi bi-collection me-1"></i>My Courses
                                    </a>
                                    <a href="{{ route('courses.progress', $course) }}" class="btn btn-outline-edunetra btn-sm">
                                        <i class="bi bi-graph-up me-1"></i>View Progress
                                    </a>
                                @else
                                    <a href="{{ route('courses.public') }}" class="btn btn-outline-edunetra btn-sm">
                                        <i class="bi bi-grid me-1"></i>Browse Courses
                                    </a>
                                @endif
                            @else
                                <a href="{{ route('courses.public') }}" class="btn btn-outline-edunetra btn-sm">
                                    <i class="bi bi-grid me-1"></i>Browse Courses
                                </a>
                                <a href="{{ route('register') }}" class="btn btn-outline-edunetra btn-sm">
                                    <i class="bi bi-person-plus me-1"></i>Create Account
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
