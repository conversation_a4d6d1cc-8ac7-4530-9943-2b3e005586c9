<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Edunetra') }} - @yield('title', 'Login')</title>

        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

        <!-- Google Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Custom Styles -->
        <style>
            :root {
                --edunetra-yellow: #FFD700;
                --edunetra-dark: #2D3748;
                --edunetra-light: #F7FAFC;
            }

            body {
                font-family: 'Inter', sans-serif;
                line-height: 1.6;
                background: linear-gradient(135deg, var(--edunetra-dark) 0%, #1a202c 100%);
                min-height: 100vh;
            }

            .bg-edunetra-yellow {
                background-color: var(--edunetra-yellow) !important;
            }

            .text-edunetra-yellow {
                color: var(--edunetra-yellow) !important;
            }

            .bg-edunetra-dark {
                background-color: var(--edunetra-dark) !important;
            }

            .text-edunetra-dark {
                color: var(--edunetra-dark) !important;
            }

            .btn-edunetra {
                background-color: var(--edunetra-yellow);
                border-color: var(--edunetra-yellow);
                color: #000;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .btn-edunetra:hover {
                background-color: #E6C200;
                border-color: #E6C200;
                color: #000;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
            }

            .login-card {
                backdrop-filter: blur(10px);
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .login-option-card {
                transition: all 0.3s ease;
                border-radius: 15px;
                cursor: pointer;
                border: 2px solid transparent;
            }

            .login-option-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
                border-color: var(--edunetra-yellow);
            }

            .login-option-card.active {
                border-color: var(--edunetra-yellow);
                background-color: rgba(255, 215, 0, 0.1);
            }

            .form-control:focus {
                border-color: var(--edunetra-yellow);
                box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
            }

            .floating-elements {
                position: absolute;
                width: 100%;
                height: 100%;
                overflow: hidden;
                pointer-events: none;
            }

            .floating-elements::before,
            .floating-elements::after {
                content: '';
                position: absolute;
                width: 200px;
                height: 200px;
                background: rgba(255, 215, 0, 0.1);
                border-radius: 50%;
                animation: float 6s ease-in-out infinite;
            }

            .floating-elements::before {
                top: 10%;
                left: 10%;
                animation-delay: 0s;
            }

            .floating-elements::after {
                bottom: 10%;
                right: 10%;
                animation-delay: 3s;
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(180deg); }
            }

            .animate-fade-in {
                animation: fadeInUp 1s ease-out;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @media (max-width: 768px) {
                .login-card {
                    margin: 1rem;
                    border-radius: 15px;
                }
            }
        </style>
    </head>
    <body>
        <div class="floating-elements"></div>

        <div class="min-vh-100 d-flex align-items-center justify-content-center p-3">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-10 col-xl-8">
                        {{ $slot }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    </body>
</html>
