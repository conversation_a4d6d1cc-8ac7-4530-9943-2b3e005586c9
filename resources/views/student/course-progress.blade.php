@extends('layouts.app')

@section('title', $course->title . ' - Progress - Edunetra')

@section('content')
<!-- Course Progress Header -->
<section class="bg-edunetra-dark text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb text-light">
                        <li class="breadcrumb-item"><a href="{{ route('my-courses') }}" class="text-light text-decoration-none">My Courses</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $course->title }}</li>
                    </ol>
                </nav>
                <h1 class="display-5 fw-bold mb-3">{{ $course->title }}</h1>
                <p class="fs-6 text-light opacity-90">Track your progress and continue learning</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="card bg-white text-dark">
                    <div class="card-body text-center p-3">
                        <div class="display-6 fw-bold text-edunetra-dark">{{ $enrollment->progress }}%</div>
                        <small class="text-muted">Course Progress</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Progress Content -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Overall Progress -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h3 class="h4 fw-bold text-edunetra-dark mb-3">
                            <i class="bi bi-graph-up me-2"></i>Overall Progress
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="bi bi-check-circle text-success fs-3 mb-2"></i>
                                    <div class="fw-bold">{{ count($enrollment->completed_sessions ?? []) }}</div>
                                    <small class="text-muted">Sessions Completed</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="bi bi-list-ol text-edunetra-dark fs-3 mb-2"></i>
                                    <div class="fw-bold">{{ $course->sessions->count() }}</div>
                                    <small class="text-muted">Total Sessions</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="progress" style="height: 12px;">
                                <div class="progress-bar bg-edunetra-yellow" role="progressbar" 
                                     style="width: {{ $enrollment->progress }}%" 
                                     aria-valuenow="{{ $enrollment->progress }}" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sessions Progress -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h3 class="h4 fw-bold text-edunetra-dark mb-0">
                            <i class="bi bi-list-check me-2"></i>Sessions Progress
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        @if($course->sessions->count() > 0)
                            @foreach($course->sessions as $session)
                                @php
                                    $isCompleted = $session->isCompleted(auth()->id());
                                @endphp
                                <div class="border-bottom p-4 {{ $loop->last ? 'border-0' : '' }}">
                                    <div class="row align-items-center">
                                        <div class="col-lg-8">
                                            <div class="d-flex align-items-start">
                                                <div class="me-3">
                                                    @if($isCompleted)
                                                        <span class="badge bg-success rounded-circle" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                            <i class="bi bi-check"></i>
                                                        </span>
                                                    @else
                                                        <span class="badge bg-light text-dark border rounded-circle" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                            {{ $session->session_number }}
                                                        </span>
                                                    @endif
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h5 class="fw-bold mb-2 {{ $isCompleted ? 'text-success' : '' }}">
                                                        {{ $session->title }}
                                                        @if($isCompleted)
                                                            <i class="bi bi-check-circle-fill text-success ms-2"></i>
                                                        @endif
                                                    </h5>
                                                    <p class="text-muted mb-2">{{ $session->description }}</p>
                                                    <div class="d-flex align-items-center text-sm">
                                                        <span class="text-muted me-3">
                                                            <i class="bi bi-clock me-1"></i>{{ $session->duration_minutes }} minutes
                                                        </span>
                                                        @if($isCompleted)
                                                            <span class="badge bg-success">
                                                                <i class="bi bi-check-circle me-1"></i>Completed
                                                            </span>
                                                        @else
                                                            <span class="badge bg-warning text-dark">
                                                                <i class="bi bi-clock me-1"></i>Pending
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                                            <a href="#" class="btn btn-edunetra btn-sm">
                                                @if($isCompleted)
                                                    <i class="bi bi-arrow-clockwise me-1"></i>Review
                                                @else
                                                    <i class="bi bi-play-circle me-1"></i>Start
                                                @endif
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="p-4 text-center text-muted">
                                <i class="bi bi-list-ol fs-1 mb-3"></i>
                                <p class="mb-0">No sessions available yet.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Course Info -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h4 class="h5 fw-bold text-edunetra-dark mb-3">Course Information</h4>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <div class="bg-edunetra-yellow rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <span class="text-dark fw-bold">{{ substr($course->teacher->name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">{{ $course->teacher->name }}</div>
                                <small class="text-muted">Instructor</small>
                            </div>
                        </div>
                        <div class="row g-2 text-center">
                            <div class="col-6">
                                <div class="p-2 bg-light rounded">
                                    <div class="fw-bold">{{ $course->duration_weeks }}</div>
                                    <small class="text-muted">Weeks</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-2 bg-light rounded">
                                    <div class="fw-bold">{{ ucfirst($course->level) }}</div>
                                    <small class="text-muted">Level</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h4 class="h5 fw-bold text-edunetra-dark mb-3">Quick Actions</h4>
                        <div class="d-grid gap-2">
                            <a href="{{ route('courses.show', $course->slug) }}" class="btn btn-edunetra">
                                <i class="bi bi-arrow-left me-1"></i>Back to Course
                            </a>
                            <a href="{{ route('my-courses') }}" class="btn btn-outline-edunetra">
                                <i class="bi bi-collection me-1"></i>My Courses
                            </a>
                            <form action="{{ route('courses.unenroll', $course) }}" method="POST" class="mt-2">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger btn-sm w-100" 
                                        onclick="return confirm('Are you sure you want to unenroll from this course?')">
                                    <i class="bi bi-x-circle me-1"></i>Unenroll from Course
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
