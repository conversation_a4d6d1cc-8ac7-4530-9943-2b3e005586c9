<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Course;
use App\Models\Enrollment;

class EnrollmentTest extends TestCase
{
    use RefreshDatabase;

    public function test_guest_cannot_enroll_in_course()
    {
        $course = Course::factory()->create(['status' => 'published']);

        $response = $this->post(route('courses.enroll', $course));

        $response->assertRedirect(route('login'));
    }

    public function test_authenticated_user_can_enroll_in_course()
    {
        $user = User::factory()->create(['role' => 'student']);
        $course = Course::factory()->create(['status' => 'published']);

        $response = $this->actingAs($user)
            ->post(route('courses.enroll', $course));

        $response->assertRedirect();
        $this->assertDatabaseHas('enrollments', [
            'user_id' => $user->id,
            'course_id' => $course->id,
            'status' => 'enrolled'
        ]);
    }

    public function test_user_cannot_enroll_twice_in_same_course()
    {
        $user = User::factory()->create(['role' => 'student']);
        $course = Course::factory()->create(['status' => 'published']);
        
        // First enrollment
        Enrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'status' => 'enrolled',
            'progress' => 0,
            'enrolled_at' => now(),
        ]);

        $response = $this->actingAs($user)
            ->post(route('courses.enroll', $course));

        $response->assertRedirect();
        $this->assertEquals(1, Enrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)->count());
    }

    public function test_user_can_view_my_courses_page()
    {
        $user = User::factory()->create(['role' => 'student']);

        $response = $this->actingAs($user)
            ->get(route('my-courses'));

        $response->assertStatus(200);
        $response->assertViewIs('student.my-courses');
    }

    public function test_guest_cannot_view_my_courses_page()
    {
        $response = $this->get(route('my-courses'));

        $response->assertRedirect(route('login'));
    }

    public function test_user_can_unenroll_from_course()
    {
        $user = User::factory()->create(['role' => 'student']);
        $course = Course::factory()->create(['status' => 'published']);
        
        $enrollment = Enrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'status' => 'enrolled',
            'progress' => 0,
            'enrolled_at' => now(),
        ]);

        $response = $this->actingAs($user)
            ->delete(route('courses.unenroll', $course));

        $response->assertRedirect();
        $this->assertDatabaseHas('enrollments', [
            'user_id' => $user->id,
            'course_id' => $course->id,
            'status' => 'dropped'
        ]);
    }
}
